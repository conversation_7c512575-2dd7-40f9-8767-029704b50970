package com.mdsadrulhasan.gogolaundry.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.mdsadrulhasan.gogolaundry.database.converters.DateConverter;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ShopItemDao_Impl implements ShopItemDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ShopItemEntity> __insertionAdapterOfShopItemEntity;

  private final EntityDeletionOrUpdateAdapter<ShopItemEntity> __deletionAdapterOfShopItemEntity;

  private final EntityDeletionOrUpdateAdapter<ShopItemEntity> __updateAdapterOfShopItemEntity;

  private final SharedSQLiteStatement __preparedStmtOfClearShopItems;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  public ShopItemDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfShopItemEntity = new EntityInsertionAdapter<ShopItemEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `shop_items` (`id`,`shop_id`,`item_id`,`custom_price`,`is_available`,`estimated_hours`,`created_at`,`itemName`,`itemBnName`,`itemImageUrl`,`defaultPrice`,`serviceName`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ShopItemEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getShopId());
        statement.bindLong(3, entity.getItemId());
        if (entity.getCustomPrice() == null) {
          statement.bindNull(4);
        } else {
          statement.bindDouble(4, entity.getCustomPrice());
        }
        final int _tmp = entity.isAvailable() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindLong(6, entity.getEstimatedHours());
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp_1);
        }
        if (entity.getItemName() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getItemName());
        }
        if (entity.getItemBnName() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getItemBnName());
        }
        if (entity.getItemImageUrl() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getItemImageUrl());
        }
        statement.bindDouble(11, entity.getDefaultPrice());
        if (entity.getServiceName() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getServiceName());
        }
      }
    };
    this.__deletionAdapterOfShopItemEntity = new EntityDeletionOrUpdateAdapter<ShopItemEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `shop_items` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ShopItemEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfShopItemEntity = new EntityDeletionOrUpdateAdapter<ShopItemEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `shop_items` SET `id` = ?,`shop_id` = ?,`item_id` = ?,`custom_price` = ?,`is_available` = ?,`estimated_hours` = ?,`created_at` = ?,`itemName` = ?,`itemBnName` = ?,`itemImageUrl` = ?,`defaultPrice` = ?,`serviceName` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ShopItemEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getShopId());
        statement.bindLong(3, entity.getItemId());
        if (entity.getCustomPrice() == null) {
          statement.bindNull(4);
        } else {
          statement.bindDouble(4, entity.getCustomPrice());
        }
        final int _tmp = entity.isAvailable() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindLong(6, entity.getEstimatedHours());
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp_1);
        }
        if (entity.getItemName() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getItemName());
        }
        if (entity.getItemBnName() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getItemBnName());
        }
        if (entity.getItemImageUrl() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getItemImageUrl());
        }
        statement.bindDouble(11, entity.getDefaultPrice());
        if (entity.getServiceName() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getServiceName());
        }
        statement.bindLong(13, entity.getId());
      }
    };
    this.__preparedStmtOfClearShopItems = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM shop_items WHERE shop_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM shop_items";
        return _query;
      }
    };
  }

  @Override
  public void insert(final ShopItemEntity shopItem) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfShopItemEntity.insert(shopItem);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void insertAll(final List<ShopItemEntity> shopItems) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfShopItemEntity.insert(shopItems);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final ShopItemEntity shopItem) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfShopItemEntity.handle(shopItem);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final ShopItemEntity shopItem) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfShopItemEntity.handle(shopItem);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void clearShopItems(final int shopId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfClearShopItems.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, shopId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfClearShopItems.release(_stmt);
    }
  }

  @Override
  public void clearAll() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfClearAll.release(_stmt);
    }
  }

  @Override
  public LiveData<List<ShopItemEntity>> getItemsByShopId(final int shopId) {
    final String _sql = "SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, i.price as defaultPrice, s.name as serviceName FROM shop_items si INNER JOIN items i ON si.item_id = i.id INNER JOIN services s ON i.service_id = s.id WHERE si.shop_id = ? AND si.is_available = 1 AND i.is_active = 1 ORDER BY s.sort_order ASC, i.name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"shop_items", "items",
        "services"}, false, new Callable<List<ShopItemEntity>>() {
      @Override
      @Nullable
      public List<ShopItemEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
          final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "item_id");
          final int _cursorIndexOfCustomPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "custom_price");
          final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfItemBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemBnName");
          final int _cursorIndexOfItemImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "itemImageUrl");
          final int _cursorIndexOfDefaultPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPrice");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final List<ShopItemEntity> _result = new ArrayList<ShopItemEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ShopItemEntity _item;
            _item = new ShopItemEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpShopId;
            _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
            _item.setShopId(_tmpShopId);
            final int _tmpItemId;
            _tmpItemId = _cursor.getInt(_cursorIndexOfItemId);
            _item.setItemId(_tmpItemId);
            final Double _tmpCustomPrice;
            if (_cursor.isNull(_cursorIndexOfCustomPrice)) {
              _tmpCustomPrice = null;
            } else {
              _tmpCustomPrice = _cursor.getDouble(_cursorIndexOfCustomPrice);
            }
            _item.setCustomPrice(_tmpCustomPrice);
            final boolean _tmpIsAvailable;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
            _tmpIsAvailable = _tmp != 0;
            _item.setAvailable(_tmpIsAvailable);
            final int _tmpEstimatedHours;
            _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
            _item.setEstimatedHours(_tmpEstimatedHours);
            final Date _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
            _item.setCreatedAt(_tmpCreatedAt);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            _item.setItemName(_tmpItemName);
            final String _tmpItemBnName;
            if (_cursor.isNull(_cursorIndexOfItemBnName)) {
              _tmpItemBnName = null;
            } else {
              _tmpItemBnName = _cursor.getString(_cursorIndexOfItemBnName);
            }
            _item.setItemBnName(_tmpItemBnName);
            final String _tmpItemImageUrl;
            if (_cursor.isNull(_cursorIndexOfItemImageUrl)) {
              _tmpItemImageUrl = null;
            } else {
              _tmpItemImageUrl = _cursor.getString(_cursorIndexOfItemImageUrl);
            }
            _item.setItemImageUrl(_tmpItemImageUrl);
            final double _tmpDefaultPrice;
            _tmpDefaultPrice = _cursor.getDouble(_cursorIndexOfDefaultPrice);
            _item.setDefaultPrice(_tmpDefaultPrice);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            _item.setServiceName(_tmpServiceName);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<ShopItemEntity> getItemsByShopIdSync(final int shopId) {
    final String _sql = "SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, i.price as defaultPrice, s.name as serviceName FROM shop_items si INNER JOIN items i ON si.item_id = i.id INNER JOIN services s ON i.service_id = s.id WHERE si.shop_id = ? AND si.is_available = 1 AND i.is_active = 1 ORDER BY s.sort_order ASC, i.name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
      final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "item_id");
      final int _cursorIndexOfCustomPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "custom_price");
      final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
      final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
      final int _cursorIndexOfItemBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemBnName");
      final int _cursorIndexOfItemImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "itemImageUrl");
      final int _cursorIndexOfDefaultPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPrice");
      final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
      final List<ShopItemEntity> _result = new ArrayList<ShopItemEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ShopItemEntity _item;
        _item = new ShopItemEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpShopId;
        _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
        _item.setShopId(_tmpShopId);
        final int _tmpItemId;
        _tmpItemId = _cursor.getInt(_cursorIndexOfItemId);
        _item.setItemId(_tmpItemId);
        final Double _tmpCustomPrice;
        if (_cursor.isNull(_cursorIndexOfCustomPrice)) {
          _tmpCustomPrice = null;
        } else {
          _tmpCustomPrice = _cursor.getDouble(_cursorIndexOfCustomPrice);
        }
        _item.setCustomPrice(_tmpCustomPrice);
        final boolean _tmpIsAvailable;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
        _tmpIsAvailable = _tmp != 0;
        _item.setAvailable(_tmpIsAvailable);
        final int _tmpEstimatedHours;
        _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
        _item.setEstimatedHours(_tmpEstimatedHours);
        final Date _tmpCreatedAt;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
        _item.setCreatedAt(_tmpCreatedAt);
        final String _tmpItemName;
        if (_cursor.isNull(_cursorIndexOfItemName)) {
          _tmpItemName = null;
        } else {
          _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
        }
        _item.setItemName(_tmpItemName);
        final String _tmpItemBnName;
        if (_cursor.isNull(_cursorIndexOfItemBnName)) {
          _tmpItemBnName = null;
        } else {
          _tmpItemBnName = _cursor.getString(_cursorIndexOfItemBnName);
        }
        _item.setItemBnName(_tmpItemBnName);
        final String _tmpItemImageUrl;
        if (_cursor.isNull(_cursorIndexOfItemImageUrl)) {
          _tmpItemImageUrl = null;
        } else {
          _tmpItemImageUrl = _cursor.getString(_cursorIndexOfItemImageUrl);
        }
        _item.setItemImageUrl(_tmpItemImageUrl);
        final double _tmpDefaultPrice;
        _tmpDefaultPrice = _cursor.getDouble(_cursorIndexOfDefaultPrice);
        _item.setDefaultPrice(_tmpDefaultPrice);
        final String _tmpServiceName;
        if (_cursor.isNull(_cursorIndexOfServiceName)) {
          _tmpServiceName = null;
        } else {
          _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
        }
        _item.setServiceName(_tmpServiceName);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<ShopItemEntity>> getItemsByShopAndService(final int shopId,
      final int serviceId) {
    final String _sql = "SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, i.price as defaultPrice, s.name as serviceName FROM shop_items si INNER JOIN items i ON si.item_id = i.id INNER JOIN services s ON i.service_id = s.id WHERE si.shop_id = ? AND i.service_id = ? AND si.is_available = 1 AND i.is_active = 1 ORDER BY i.name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, serviceId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"shop_items", "items",
        "services"}, false, new Callable<List<ShopItemEntity>>() {
      @Override
      @Nullable
      public List<ShopItemEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
          final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "item_id");
          final int _cursorIndexOfCustomPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "custom_price");
          final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfItemBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemBnName");
          final int _cursorIndexOfItemImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "itemImageUrl");
          final int _cursorIndexOfDefaultPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPrice");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final List<ShopItemEntity> _result = new ArrayList<ShopItemEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ShopItemEntity _item;
            _item = new ShopItemEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpShopId;
            _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
            _item.setShopId(_tmpShopId);
            final int _tmpItemId;
            _tmpItemId = _cursor.getInt(_cursorIndexOfItemId);
            _item.setItemId(_tmpItemId);
            final Double _tmpCustomPrice;
            if (_cursor.isNull(_cursorIndexOfCustomPrice)) {
              _tmpCustomPrice = null;
            } else {
              _tmpCustomPrice = _cursor.getDouble(_cursorIndexOfCustomPrice);
            }
            _item.setCustomPrice(_tmpCustomPrice);
            final boolean _tmpIsAvailable;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
            _tmpIsAvailable = _tmp != 0;
            _item.setAvailable(_tmpIsAvailable);
            final int _tmpEstimatedHours;
            _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
            _item.setEstimatedHours(_tmpEstimatedHours);
            final Date _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
            _item.setCreatedAt(_tmpCreatedAt);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            _item.setItemName(_tmpItemName);
            final String _tmpItemBnName;
            if (_cursor.isNull(_cursorIndexOfItemBnName)) {
              _tmpItemBnName = null;
            } else {
              _tmpItemBnName = _cursor.getString(_cursorIndexOfItemBnName);
            }
            _item.setItemBnName(_tmpItemBnName);
            final String _tmpItemImageUrl;
            if (_cursor.isNull(_cursorIndexOfItemImageUrl)) {
              _tmpItemImageUrl = null;
            } else {
              _tmpItemImageUrl = _cursor.getString(_cursorIndexOfItemImageUrl);
            }
            _item.setItemImageUrl(_tmpItemImageUrl);
            final double _tmpDefaultPrice;
            _tmpDefaultPrice = _cursor.getDouble(_cursorIndexOfDefaultPrice);
            _item.setDefaultPrice(_tmpDefaultPrice);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            _item.setServiceName(_tmpServiceName);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<ShopItemEntity>> getShopsByItemId(final int itemId) {
    final String _sql = "SELECT si.* FROM shop_items si INNER JOIN laundry_shops ls ON si.shop_id = ls.id WHERE si.item_id = ? AND si.is_available = 1 AND ls.is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, itemId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"shop_items",
        "laundry_shops"}, false, new Callable<List<ShopItemEntity>>() {
      @Override
      @Nullable
      public List<ShopItemEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
          final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "item_id");
          final int _cursorIndexOfCustomPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "custom_price");
          final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfItemBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemBnName");
          final int _cursorIndexOfItemImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "itemImageUrl");
          final int _cursorIndexOfDefaultPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPrice");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final List<ShopItemEntity> _result = new ArrayList<ShopItemEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ShopItemEntity _item;
            _item = new ShopItemEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpShopId;
            _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
            _item.setShopId(_tmpShopId);
            final int _tmpItemId;
            _tmpItemId = _cursor.getInt(_cursorIndexOfItemId);
            _item.setItemId(_tmpItemId);
            final Double _tmpCustomPrice;
            if (_cursor.isNull(_cursorIndexOfCustomPrice)) {
              _tmpCustomPrice = null;
            } else {
              _tmpCustomPrice = _cursor.getDouble(_cursorIndexOfCustomPrice);
            }
            _item.setCustomPrice(_tmpCustomPrice);
            final boolean _tmpIsAvailable;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
            _tmpIsAvailable = _tmp != 0;
            _item.setAvailable(_tmpIsAvailable);
            final int _tmpEstimatedHours;
            _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
            _item.setEstimatedHours(_tmpEstimatedHours);
            final Date _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
            _item.setCreatedAt(_tmpCreatedAt);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            _item.setItemName(_tmpItemName);
            final String _tmpItemBnName;
            if (_cursor.isNull(_cursorIndexOfItemBnName)) {
              _tmpItemBnName = null;
            } else {
              _tmpItemBnName = _cursor.getString(_cursorIndexOfItemBnName);
            }
            _item.setItemBnName(_tmpItemBnName);
            final String _tmpItemImageUrl;
            if (_cursor.isNull(_cursorIndexOfItemImageUrl)) {
              _tmpItemImageUrl = null;
            } else {
              _tmpItemImageUrl = _cursor.getString(_cursorIndexOfItemImageUrl);
            }
            _item.setItemImageUrl(_tmpItemImageUrl);
            final double _tmpDefaultPrice;
            _tmpDefaultPrice = _cursor.getDouble(_cursorIndexOfDefaultPrice);
            _item.setDefaultPrice(_tmpDefaultPrice);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            _item.setServiceName(_tmpServiceName);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public boolean doesShopOfferItem(final int shopId, final int itemId) {
    final String _sql = "SELECT COUNT(*) > 0 FROM shop_items WHERE shop_id = ? AND item_id = ? AND is_available = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, itemId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final boolean _result;
      if (_cursor.moveToFirst()) {
        final int _tmp;
        _tmp = _cursor.getInt(0);
        _result = _tmp != 0;
      } else {
        _result = false;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public ShopItemEntity getShopItem(final int shopId, final int itemId) {
    final String _sql = "SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, i.price as defaultPrice, s.name as serviceName FROM shop_items si INNER JOIN items i ON si.item_id = i.id INNER JOIN services s ON i.service_id = s.id WHERE si.shop_id = ? AND si.item_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, itemId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
      final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "item_id");
      final int _cursorIndexOfCustomPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "custom_price");
      final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
      final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
      final int _cursorIndexOfItemBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemBnName");
      final int _cursorIndexOfItemImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "itemImageUrl");
      final int _cursorIndexOfDefaultPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPrice");
      final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
      final ShopItemEntity _result;
      if (_cursor.moveToFirst()) {
        _result = new ShopItemEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final int _tmpShopId;
        _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
        _result.setShopId(_tmpShopId);
        final int _tmpItemId;
        _tmpItemId = _cursor.getInt(_cursorIndexOfItemId);
        _result.setItemId(_tmpItemId);
        final Double _tmpCustomPrice;
        if (_cursor.isNull(_cursorIndexOfCustomPrice)) {
          _tmpCustomPrice = null;
        } else {
          _tmpCustomPrice = _cursor.getDouble(_cursorIndexOfCustomPrice);
        }
        _result.setCustomPrice(_tmpCustomPrice);
        final boolean _tmpIsAvailable;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
        _tmpIsAvailable = _tmp != 0;
        _result.setAvailable(_tmpIsAvailable);
        final int _tmpEstimatedHours;
        _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
        _result.setEstimatedHours(_tmpEstimatedHours);
        final Date _tmpCreatedAt;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
        _result.setCreatedAt(_tmpCreatedAt);
        final String _tmpItemName;
        if (_cursor.isNull(_cursorIndexOfItemName)) {
          _tmpItemName = null;
        } else {
          _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
        }
        _result.setItemName(_tmpItemName);
        final String _tmpItemBnName;
        if (_cursor.isNull(_cursorIndexOfItemBnName)) {
          _tmpItemBnName = null;
        } else {
          _tmpItemBnName = _cursor.getString(_cursorIndexOfItemBnName);
        }
        _result.setItemBnName(_tmpItemBnName);
        final String _tmpItemImageUrl;
        if (_cursor.isNull(_cursorIndexOfItemImageUrl)) {
          _tmpItemImageUrl = null;
        } else {
          _tmpItemImageUrl = _cursor.getString(_cursorIndexOfItemImageUrl);
        }
        _result.setItemImageUrl(_tmpItemImageUrl);
        final double _tmpDefaultPrice;
        _tmpDefaultPrice = _cursor.getDouble(_cursorIndexOfDefaultPrice);
        _result.setDefaultPrice(_tmpDefaultPrice);
        final String _tmpServiceName;
        if (_cursor.isNull(_cursorIndexOfServiceName)) {
          _tmpServiceName = null;
        } else {
          _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
        }
        _result.setServiceName(_tmpServiceName);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<Integer> getItemCountByShop(final int shopId) {
    final String _sql = "SELECT COUNT(*) FROM shop_items WHERE shop_id = ? AND is_available = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"shop_items"}, false, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<ShopItemEntity>> searchItemsInShop(final int shopId, final String query) {
    final String _sql = "SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, i.price as defaultPrice, s.name as serviceName FROM shop_items si INNER JOIN items i ON si.item_id = i.id INNER JOIN services s ON i.service_id = s.id WHERE si.shop_id = ? AND si.is_available = 1 AND i.is_active = 1 AND (i.name LIKE '%' || ? || '%' OR i.bn_name LIKE '%' || ? || '%') ORDER BY i.name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    _argIndex = 2;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 3;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"shop_items", "items",
        "services"}, false, new Callable<List<ShopItemEntity>>() {
      @Override
      @Nullable
      public List<ShopItemEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
          final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "item_id");
          final int _cursorIndexOfCustomPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "custom_price");
          final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfItemBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemBnName");
          final int _cursorIndexOfItemImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "itemImageUrl");
          final int _cursorIndexOfDefaultPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPrice");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final List<ShopItemEntity> _result = new ArrayList<ShopItemEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ShopItemEntity _item;
            _item = new ShopItemEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpShopId;
            _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
            _item.setShopId(_tmpShopId);
            final int _tmpItemId;
            _tmpItemId = _cursor.getInt(_cursorIndexOfItemId);
            _item.setItemId(_tmpItemId);
            final Double _tmpCustomPrice;
            if (_cursor.isNull(_cursorIndexOfCustomPrice)) {
              _tmpCustomPrice = null;
            } else {
              _tmpCustomPrice = _cursor.getDouble(_cursorIndexOfCustomPrice);
            }
            _item.setCustomPrice(_tmpCustomPrice);
            final boolean _tmpIsAvailable;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
            _tmpIsAvailable = _tmp != 0;
            _item.setAvailable(_tmpIsAvailable);
            final int _tmpEstimatedHours;
            _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
            _item.setEstimatedHours(_tmpEstimatedHours);
            final Date _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
            _item.setCreatedAt(_tmpCreatedAt);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            _item.setItemName(_tmpItemName);
            final String _tmpItemBnName;
            if (_cursor.isNull(_cursorIndexOfItemBnName)) {
              _tmpItemBnName = null;
            } else {
              _tmpItemBnName = _cursor.getString(_cursorIndexOfItemBnName);
            }
            _item.setItemBnName(_tmpItemBnName);
            final String _tmpItemImageUrl;
            if (_cursor.isNull(_cursorIndexOfItemImageUrl)) {
              _tmpItemImageUrl = null;
            } else {
              _tmpItemImageUrl = _cursor.getString(_cursorIndexOfItemImageUrl);
            }
            _item.setItemImageUrl(_tmpItemImageUrl);
            final double _tmpDefaultPrice;
            _tmpDefaultPrice = _cursor.getDouble(_cursorIndexOfDefaultPrice);
            _item.setDefaultPrice(_tmpDefaultPrice);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            _item.setServiceName(_tmpServiceName);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
