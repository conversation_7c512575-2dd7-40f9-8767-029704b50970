package com.mdsadrulhasan.gogolaundry.network;

import com.mdsadrulhasan.gogolaundry.model.AdminPaymentConfig;
import com.mdsadrulhasan.gogolaundry.model.ApiResponse;

import retrofit2.Call;
import retrofit2.http.GET;

/**
 * API service interface for admin payment configuration
 * Fetches admin payment numbers and configuration for checkout
 */
public interface AdminPaymentConfigService {

    /**
     * Get admin payment configuration including payment numbers
     * Users must pay to these admin accounts, not directly to shop owners
     */
    @GET("admin_payment_config.php")
    Call<ApiResponse<AdminPaymentConfig>> getAdminPaymentConfig();
}
