package com.mdsadrulhasan.gogolaundry.database.entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import java.util.Date;

/**
 * Shop Service entity for Room database
 */
@Entity(
    tableName = "shop_services",
    foreignKeys = {
        @ForeignKey(
            entity = LaundryShopEntity.class,
            parentColumns = "id",
            childColumns = "shop_id",
            onDelete = ForeignKey.CASCADE
        ),
        @ForeignKey(
            entity = ServiceEntity.class,
            parentColumns = "id",
            childColumns = "service_id",
            onDelete = ForeignKey.CASCADE
        )
    },
    indices = {
        @Index(value = {"shop_id", "service_id"}, unique = true),
        @Index(value = "shop_id"),
        @Index(value = "service_id")
    }
)
public class ShopServiceEntity {

    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "shop_id")
    private int shopId;

    @ColumnInfo(name = "service_id")
    private int serviceId;

    @ColumnInfo(name = "is_available")
    private boolean isAvailable;

    @ColumnInfo(name = "estimated_hours")
    private int estimatedHours;

    @ColumnInfo(name = "created_at")
    private Date createdAt;

    // Additional fields for display purposes
    private String serviceName;
    private String serviceBnName;
    private String serviceImageUrl;
    private String description;
    private String bnDescription;
    private double basePrice;

    // Getters and setters

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public int getServiceId() {
        return serviceId;
    }

    public void setServiceId(int serviceId) {
        this.serviceId = serviceId;
    }

    public boolean isAvailable() {
        return isAvailable;
    }

    public void setAvailable(boolean available) {
        isAvailable = available;
    }

    public int getEstimatedHours() {
        return estimatedHours;
    }

    public void setEstimatedHours(int estimatedHours) {
        this.estimatedHours = estimatedHours;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServiceBnName() {
        return serviceBnName;
    }

    public void setServiceBnName(String serviceBnName) {
        this.serviceBnName = serviceBnName;
    }

    public String getServiceImageUrl() {
        return serviceImageUrl;
    }

    public void setServiceImageUrl(String serviceImageUrl) {
        this.serviceImageUrl = serviceImageUrl;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBnDescription() {
        return bnDescription;
    }

    public void setBnDescription(String bnDescription) {
        this.bnDescription = bnDescription;
    }

    public double getBasePrice() {
        return basePrice;
    }

    public void setBasePrice(double basePrice) {
        this.basePrice = basePrice;
    }
}
