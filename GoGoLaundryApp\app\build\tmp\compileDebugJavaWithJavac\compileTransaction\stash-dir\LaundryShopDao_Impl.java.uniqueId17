package com.mdsadrulhasan.gogolaundry.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.mdsadrulhasan.gogolaundry.database.converters.DateConverter;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class LaundryShopDao_Impl implements LaundryShopDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<LaundryShopEntity> __insertionAdapterOfLaundryShopEntity;

  private final EntityDeletionOrUpdateAdapter<LaundryShopEntity> __deletionAdapterOfLaundryShopEntity;

  private final EntityDeletionOrUpdateAdapter<LaundryShopEntity> __updateAdapterOfLaundryShopEntity;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  public LaundryShopDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfLaundryShopEntity = new EntityInsertionAdapter<LaundryShopEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `laundry_shops` (`id`,`name`,`bn_name`,`description`,`bn_description`,`owner_name`,`phone`,`email`,`address`,`division_id`,`district_id`,`upazilla_id`,`latitude`,`longitude`,`operating_hours`,`rating`,`total_reviews`,`commission_percentage`,`is_active`,`is_verified`,`profile_image_url`,`cover_image_url`,`created_at`,`updated_at`,`divisionName`,`districtName`,`upazillaName`,`distance`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final LaundryShopEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getBnName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getBnName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getBnDescription() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getBnDescription());
        }
        if (entity.getOwnerName() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getOwnerName());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPhone());
        }
        if (entity.getEmail() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getEmail());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getAddress());
        }
        if (entity.getDivisionId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, entity.getDivisionId());
        }
        if (entity.getDistrictId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getDistrictId());
        }
        if (entity.getUpazillaId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getUpazillaId());
        }
        statement.bindDouble(13, entity.getLatitude());
        statement.bindDouble(14, entity.getLongitude());
        if (entity.getOperatingHours() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getOperatingHours());
        }
        statement.bindDouble(16, entity.getRating());
        statement.bindLong(17, entity.getTotalReviews());
        statement.bindDouble(18, entity.getCommissionPercentage());
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(19, _tmp);
        final int _tmp_1 = entity.isVerified() ? 1 : 0;
        statement.bindLong(20, _tmp_1);
        if (entity.getProfileImageUrl() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getProfileImageUrl());
        }
        if (entity.getCoverImageUrl() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getCoverImageUrl());
        }
        final Long _tmp_2 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(23);
        } else {
          statement.bindLong(23, _tmp_2);
        }
        final Long _tmp_3 = DateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(24);
        } else {
          statement.bindLong(24, _tmp_3);
        }
        if (entity.getDivisionName() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getDivisionName());
        }
        if (entity.getDistrictName() == null) {
          statement.bindNull(26);
        } else {
          statement.bindString(26, entity.getDistrictName());
        }
        if (entity.getUpazillaName() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getUpazillaName());
        }
        statement.bindDouble(28, entity.getDistance());
      }
    };
    this.__deletionAdapterOfLaundryShopEntity = new EntityDeletionOrUpdateAdapter<LaundryShopEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `laundry_shops` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final LaundryShopEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfLaundryShopEntity = new EntityDeletionOrUpdateAdapter<LaundryShopEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `laundry_shops` SET `id` = ?,`name` = ?,`bn_name` = ?,`description` = ?,`bn_description` = ?,`owner_name` = ?,`phone` = ?,`email` = ?,`address` = ?,`division_id` = ?,`district_id` = ?,`upazilla_id` = ?,`latitude` = ?,`longitude` = ?,`operating_hours` = ?,`rating` = ?,`total_reviews` = ?,`commission_percentage` = ?,`is_active` = ?,`is_verified` = ?,`profile_image_url` = ?,`cover_image_url` = ?,`created_at` = ?,`updated_at` = ?,`divisionName` = ?,`districtName` = ?,`upazillaName` = ?,`distance` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final LaundryShopEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getBnName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getBnName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getBnDescription() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getBnDescription());
        }
        if (entity.getOwnerName() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getOwnerName());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPhone());
        }
        if (entity.getEmail() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getEmail());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getAddress());
        }
        if (entity.getDivisionId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, entity.getDivisionId());
        }
        if (entity.getDistrictId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getDistrictId());
        }
        if (entity.getUpazillaId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getUpazillaId());
        }
        statement.bindDouble(13, entity.getLatitude());
        statement.bindDouble(14, entity.getLongitude());
        if (entity.getOperatingHours() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getOperatingHours());
        }
        statement.bindDouble(16, entity.getRating());
        statement.bindLong(17, entity.getTotalReviews());
        statement.bindDouble(18, entity.getCommissionPercentage());
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(19, _tmp);
        final int _tmp_1 = entity.isVerified() ? 1 : 0;
        statement.bindLong(20, _tmp_1);
        if (entity.getProfileImageUrl() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getProfileImageUrl());
        }
        if (entity.getCoverImageUrl() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getCoverImageUrl());
        }
        final Long _tmp_2 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(23);
        } else {
          statement.bindLong(23, _tmp_2);
        }
        final Long _tmp_3 = DateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(24);
        } else {
          statement.bindLong(24, _tmp_3);
        }
        if (entity.getDivisionName() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getDivisionName());
        }
        if (entity.getDistrictName() == null) {
          statement.bindNull(26);
        } else {
          statement.bindString(26, entity.getDistrictName());
        }
        if (entity.getUpazillaName() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getUpazillaName());
        }
        statement.bindDouble(28, entity.getDistance());
        statement.bindLong(29, entity.getId());
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM laundry_shops";
        return _query;
      }
    };
  }

  @Override
  public void insert(final LaundryShopEntity shop) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfLaundryShopEntity.insert(shop);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void insertAll(final List<LaundryShopEntity> shops) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfLaundryShopEntity.insert(shops);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final LaundryShopEntity shop) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfLaundryShopEntity.handle(shop);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final LaundryShopEntity shop) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfLaundryShopEntity.handle(shop);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void clearAll() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfClearAll.release(_stmt);
    }
  }

  @Override
  public LiveData<List<LaundryShopEntity>> getAllShops() {
    final String _sql = "SELECT * FROM laundry_shops WHERE is_active = 1 ORDER BY rating DESC, total_reviews DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"laundry_shops"}, false, new Callable<List<LaundryShopEntity>>() {
      @Override
      @Nullable
      public List<LaundryShopEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
          final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
          final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
          final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
          final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
          final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
          final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
          final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
          final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
          final List<LaundryShopEntity> _result = new ArrayList<LaundryShopEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final LaundryShopEntity _item;
            _item = new LaundryShopEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            _item.setName(_tmpName);
            final String _tmpBnName;
            if (_cursor.isNull(_cursorIndexOfBnName)) {
              _tmpBnName = null;
            } else {
              _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
            }
            _item.setBnName(_tmpBnName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _item.setBnDescription(_tmpBnDescription);
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            _item.setOwnerName(_tmpOwnerName);
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            _item.setPhone(_tmpPhone);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            _item.setEmail(_tmpEmail);
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item.setAddress(_tmpAddress);
            final Integer _tmpDivisionId;
            if (_cursor.isNull(_cursorIndexOfDivisionId)) {
              _tmpDivisionId = null;
            } else {
              _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
            }
            _item.setDivisionId(_tmpDivisionId);
            final Integer _tmpDistrictId;
            if (_cursor.isNull(_cursorIndexOfDistrictId)) {
              _tmpDistrictId = null;
            } else {
              _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
            }
            _item.setDistrictId(_tmpDistrictId);
            final Integer _tmpUpazillaId;
            if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
              _tmpUpazillaId = null;
            } else {
              _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
            }
            _item.setUpazillaId(_tmpUpazillaId);
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            _item.setLatitude(_tmpLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            _item.setLongitude(_tmpLongitude);
            final String _tmpOperatingHours;
            if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
              _tmpOperatingHours = null;
            } else {
              _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
            }
            _item.setOperatingHours(_tmpOperatingHours);
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            _item.setRating(_tmpRating);
            final int _tmpTotalReviews;
            _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
            _item.setTotalReviews(_tmpTotalReviews);
            final double _tmpCommissionPercentage;
            _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
            _item.setCommissionPercentage(_tmpCommissionPercentage);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item.setActive(_tmpIsActive);
            final boolean _tmpIsVerified;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
            _tmpIsVerified = _tmp_1 != 0;
            _item.setVerified(_tmpIsVerified);
            final String _tmpProfileImageUrl;
            if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
              _tmpProfileImageUrl = null;
            } else {
              _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
            }
            _item.setProfileImageUrl(_tmpProfileImageUrl);
            final String _tmpCoverImageUrl;
            if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
              _tmpCoverImageUrl = null;
            } else {
              _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
            }
            _item.setCoverImageUrl(_tmpCoverImageUrl);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _item.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _item.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpDivisionName;
            if (_cursor.isNull(_cursorIndexOfDivisionName)) {
              _tmpDivisionName = null;
            } else {
              _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
            }
            _item.setDivisionName(_tmpDivisionName);
            final String _tmpDistrictName;
            if (_cursor.isNull(_cursorIndexOfDistrictName)) {
              _tmpDistrictName = null;
            } else {
              _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
            }
            _item.setDistrictName(_tmpDistrictName);
            final String _tmpUpazillaName;
            if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
              _tmpUpazillaName = null;
            } else {
              _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
            }
            _item.setUpazillaName(_tmpUpazillaName);
            final double _tmpDistance;
            _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
            _item.setDistance(_tmpDistance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<LaundryShopEntity> getShopById(final int shopId) {
    final String _sql = "SELECT * FROM laundry_shops WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"laundry_shops"}, false, new Callable<LaundryShopEntity>() {
      @Override
      @Nullable
      public LaundryShopEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
          final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
          final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
          final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
          final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
          final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
          final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
          final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
          final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
          final LaundryShopEntity _result;
          if (_cursor.moveToFirst()) {
            _result = new LaundryShopEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _result.setId(_tmpId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            _result.setName(_tmpName);
            final String _tmpBnName;
            if (_cursor.isNull(_cursorIndexOfBnName)) {
              _tmpBnName = null;
            } else {
              _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
            }
            _result.setBnName(_tmpBnName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _result.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _result.setBnDescription(_tmpBnDescription);
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            _result.setOwnerName(_tmpOwnerName);
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            _result.setPhone(_tmpPhone);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            _result.setEmail(_tmpEmail);
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _result.setAddress(_tmpAddress);
            final Integer _tmpDivisionId;
            if (_cursor.isNull(_cursorIndexOfDivisionId)) {
              _tmpDivisionId = null;
            } else {
              _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
            }
            _result.setDivisionId(_tmpDivisionId);
            final Integer _tmpDistrictId;
            if (_cursor.isNull(_cursorIndexOfDistrictId)) {
              _tmpDistrictId = null;
            } else {
              _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
            }
            _result.setDistrictId(_tmpDistrictId);
            final Integer _tmpUpazillaId;
            if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
              _tmpUpazillaId = null;
            } else {
              _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
            }
            _result.setUpazillaId(_tmpUpazillaId);
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            _result.setLatitude(_tmpLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            _result.setLongitude(_tmpLongitude);
            final String _tmpOperatingHours;
            if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
              _tmpOperatingHours = null;
            } else {
              _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
            }
            _result.setOperatingHours(_tmpOperatingHours);
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            _result.setRating(_tmpRating);
            final int _tmpTotalReviews;
            _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
            _result.setTotalReviews(_tmpTotalReviews);
            final double _tmpCommissionPercentage;
            _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
            _result.setCommissionPercentage(_tmpCommissionPercentage);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _result.setActive(_tmpIsActive);
            final boolean _tmpIsVerified;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
            _tmpIsVerified = _tmp_1 != 0;
            _result.setVerified(_tmpIsVerified);
            final String _tmpProfileImageUrl;
            if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
              _tmpProfileImageUrl = null;
            } else {
              _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
            }
            _result.setProfileImageUrl(_tmpProfileImageUrl);
            final String _tmpCoverImageUrl;
            if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
              _tmpCoverImageUrl = null;
            } else {
              _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
            }
            _result.setCoverImageUrl(_tmpCoverImageUrl);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _result.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _result.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpDivisionName;
            if (_cursor.isNull(_cursorIndexOfDivisionName)) {
              _tmpDivisionName = null;
            } else {
              _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
            }
            _result.setDivisionName(_tmpDivisionName);
            final String _tmpDistrictName;
            if (_cursor.isNull(_cursorIndexOfDistrictName)) {
              _tmpDistrictName = null;
            } else {
              _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
            }
            _result.setDistrictName(_tmpDistrictName);
            final String _tmpUpazillaName;
            if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
              _tmpUpazillaName = null;
            } else {
              _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
            }
            _result.setUpazillaName(_tmpUpazillaName);
            final double _tmpDistance;
            _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
            _result.setDistance(_tmpDistance);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LaundryShopEntity getShopByIdSync(final int shopId) {
    final String _sql = "SELECT * FROM laundry_shops WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
      final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
      final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
      final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
      final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
      final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
      final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
      final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
      final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
      final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
      final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
      final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
      final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
      final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
      final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
      final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
      final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
      final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
      final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
      final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
      final LaundryShopEntity _result;
      if (_cursor.moveToFirst()) {
        _result = new LaundryShopEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpName;
        if (_cursor.isNull(_cursorIndexOfName)) {
          _tmpName = null;
        } else {
          _tmpName = _cursor.getString(_cursorIndexOfName);
        }
        _result.setName(_tmpName);
        final String _tmpBnName;
        if (_cursor.isNull(_cursorIndexOfBnName)) {
          _tmpBnName = null;
        } else {
          _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
        }
        _result.setBnName(_tmpBnName);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _result.setDescription(_tmpDescription);
        final String _tmpBnDescription;
        if (_cursor.isNull(_cursorIndexOfBnDescription)) {
          _tmpBnDescription = null;
        } else {
          _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
        }
        _result.setBnDescription(_tmpBnDescription);
        final String _tmpOwnerName;
        if (_cursor.isNull(_cursorIndexOfOwnerName)) {
          _tmpOwnerName = null;
        } else {
          _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
        }
        _result.setOwnerName(_tmpOwnerName);
        final String _tmpPhone;
        if (_cursor.isNull(_cursorIndexOfPhone)) {
          _tmpPhone = null;
        } else {
          _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
        }
        _result.setPhone(_tmpPhone);
        final String _tmpEmail;
        if (_cursor.isNull(_cursorIndexOfEmail)) {
          _tmpEmail = null;
        } else {
          _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
        }
        _result.setEmail(_tmpEmail);
        final String _tmpAddress;
        if (_cursor.isNull(_cursorIndexOfAddress)) {
          _tmpAddress = null;
        } else {
          _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
        }
        _result.setAddress(_tmpAddress);
        final Integer _tmpDivisionId;
        if (_cursor.isNull(_cursorIndexOfDivisionId)) {
          _tmpDivisionId = null;
        } else {
          _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
        }
        _result.setDivisionId(_tmpDivisionId);
        final Integer _tmpDistrictId;
        if (_cursor.isNull(_cursorIndexOfDistrictId)) {
          _tmpDistrictId = null;
        } else {
          _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
        }
        _result.setDistrictId(_tmpDistrictId);
        final Integer _tmpUpazillaId;
        if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
          _tmpUpazillaId = null;
        } else {
          _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
        }
        _result.setUpazillaId(_tmpUpazillaId);
        final double _tmpLatitude;
        _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
        _result.setLatitude(_tmpLatitude);
        final double _tmpLongitude;
        _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
        _result.setLongitude(_tmpLongitude);
        final String _tmpOperatingHours;
        if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
          _tmpOperatingHours = null;
        } else {
          _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
        }
        _result.setOperatingHours(_tmpOperatingHours);
        final double _tmpRating;
        _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
        _result.setRating(_tmpRating);
        final int _tmpTotalReviews;
        _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
        _result.setTotalReviews(_tmpTotalReviews);
        final double _tmpCommissionPercentage;
        _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
        _result.setCommissionPercentage(_tmpCommissionPercentage);
        final boolean _tmpIsActive;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp != 0;
        _result.setActive(_tmpIsActive);
        final boolean _tmpIsVerified;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
        _tmpIsVerified = _tmp_1 != 0;
        _result.setVerified(_tmpIsVerified);
        final String _tmpProfileImageUrl;
        if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
          _tmpProfileImageUrl = null;
        } else {
          _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
        }
        _result.setProfileImageUrl(_tmpProfileImageUrl);
        final String _tmpCoverImageUrl;
        if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
          _tmpCoverImageUrl = null;
        } else {
          _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
        }
        _result.setCoverImageUrl(_tmpCoverImageUrl);
        final Date _tmpCreatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
        _result.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
        _result.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpDivisionName;
        if (_cursor.isNull(_cursorIndexOfDivisionName)) {
          _tmpDivisionName = null;
        } else {
          _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
        }
        _result.setDivisionName(_tmpDivisionName);
        final String _tmpDistrictName;
        if (_cursor.isNull(_cursorIndexOfDistrictName)) {
          _tmpDistrictName = null;
        } else {
          _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
        }
        _result.setDistrictName(_tmpDistrictName);
        final String _tmpUpazillaName;
        if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
          _tmpUpazillaName = null;
        } else {
          _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
        }
        _result.setUpazillaName(_tmpUpazillaName);
        final double _tmpDistance;
        _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
        _result.setDistance(_tmpDistance);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<LaundryShopEntity>> getShopsByLocation(final Integer divisionId,
      final Integer districtId, final Integer upazillaId) {
    final String _sql = "SELECT * FROM laundry_shops WHERE is_active = 1 AND (? IS NULL OR division_id = ?) AND (? IS NULL OR district_id = ?) AND (? IS NULL OR upazilla_id = ?) ORDER BY rating DESC, total_reviews DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 6);
    int _argIndex = 1;
    if (divisionId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, divisionId);
    }
    _argIndex = 2;
    if (divisionId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, divisionId);
    }
    _argIndex = 3;
    if (districtId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, districtId);
    }
    _argIndex = 4;
    if (districtId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, districtId);
    }
    _argIndex = 5;
    if (upazillaId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, upazillaId);
    }
    _argIndex = 6;
    if (upazillaId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, upazillaId);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"laundry_shops"}, false, new Callable<List<LaundryShopEntity>>() {
      @Override
      @Nullable
      public List<LaundryShopEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
          final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
          final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
          final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
          final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
          final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
          final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
          final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
          final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
          final List<LaundryShopEntity> _result = new ArrayList<LaundryShopEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final LaundryShopEntity _item;
            _item = new LaundryShopEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            _item.setName(_tmpName);
            final String _tmpBnName;
            if (_cursor.isNull(_cursorIndexOfBnName)) {
              _tmpBnName = null;
            } else {
              _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
            }
            _item.setBnName(_tmpBnName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _item.setBnDescription(_tmpBnDescription);
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            _item.setOwnerName(_tmpOwnerName);
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            _item.setPhone(_tmpPhone);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            _item.setEmail(_tmpEmail);
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item.setAddress(_tmpAddress);
            final Integer _tmpDivisionId;
            if (_cursor.isNull(_cursorIndexOfDivisionId)) {
              _tmpDivisionId = null;
            } else {
              _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
            }
            _item.setDivisionId(_tmpDivisionId);
            final Integer _tmpDistrictId;
            if (_cursor.isNull(_cursorIndexOfDistrictId)) {
              _tmpDistrictId = null;
            } else {
              _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
            }
            _item.setDistrictId(_tmpDistrictId);
            final Integer _tmpUpazillaId;
            if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
              _tmpUpazillaId = null;
            } else {
              _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
            }
            _item.setUpazillaId(_tmpUpazillaId);
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            _item.setLatitude(_tmpLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            _item.setLongitude(_tmpLongitude);
            final String _tmpOperatingHours;
            if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
              _tmpOperatingHours = null;
            } else {
              _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
            }
            _item.setOperatingHours(_tmpOperatingHours);
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            _item.setRating(_tmpRating);
            final int _tmpTotalReviews;
            _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
            _item.setTotalReviews(_tmpTotalReviews);
            final double _tmpCommissionPercentage;
            _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
            _item.setCommissionPercentage(_tmpCommissionPercentage);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item.setActive(_tmpIsActive);
            final boolean _tmpIsVerified;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
            _tmpIsVerified = _tmp_1 != 0;
            _item.setVerified(_tmpIsVerified);
            final String _tmpProfileImageUrl;
            if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
              _tmpProfileImageUrl = null;
            } else {
              _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
            }
            _item.setProfileImageUrl(_tmpProfileImageUrl);
            final String _tmpCoverImageUrl;
            if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
              _tmpCoverImageUrl = null;
            } else {
              _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
            }
            _item.setCoverImageUrl(_tmpCoverImageUrl);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _item.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _item.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpDivisionName;
            if (_cursor.isNull(_cursorIndexOfDivisionName)) {
              _tmpDivisionName = null;
            } else {
              _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
            }
            _item.setDivisionName(_tmpDivisionName);
            final String _tmpDistrictName;
            if (_cursor.isNull(_cursorIndexOfDistrictName)) {
              _tmpDistrictName = null;
            } else {
              _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
            }
            _item.setDistrictName(_tmpDistrictName);
            final String _tmpUpazillaName;
            if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
              _tmpUpazillaName = null;
            } else {
              _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
            }
            _item.setUpazillaName(_tmpUpazillaName);
            final double _tmpDistance;
            _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
            _item.setDistance(_tmpDistance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<LaundryShopEntity>> searchShops(final String query) {
    final String _sql = "SELECT * FROM laundry_shops WHERE (name LIKE '%' || ? || '%' OR bn_name LIKE '%' || ? || '%' OR address LIKE '%' || ? || '%') ORDER BY is_active DESC, rating DESC, total_reviews DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 2;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 3;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"laundry_shops"}, false, new Callable<List<LaundryShopEntity>>() {
      @Override
      @Nullable
      public List<LaundryShopEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
          final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
          final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
          final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
          final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
          final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
          final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
          final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
          final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
          final List<LaundryShopEntity> _result = new ArrayList<LaundryShopEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final LaundryShopEntity _item;
            _item = new LaundryShopEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            _item.setName(_tmpName);
            final String _tmpBnName;
            if (_cursor.isNull(_cursorIndexOfBnName)) {
              _tmpBnName = null;
            } else {
              _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
            }
            _item.setBnName(_tmpBnName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _item.setBnDescription(_tmpBnDescription);
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            _item.setOwnerName(_tmpOwnerName);
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            _item.setPhone(_tmpPhone);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            _item.setEmail(_tmpEmail);
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item.setAddress(_tmpAddress);
            final Integer _tmpDivisionId;
            if (_cursor.isNull(_cursorIndexOfDivisionId)) {
              _tmpDivisionId = null;
            } else {
              _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
            }
            _item.setDivisionId(_tmpDivisionId);
            final Integer _tmpDistrictId;
            if (_cursor.isNull(_cursorIndexOfDistrictId)) {
              _tmpDistrictId = null;
            } else {
              _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
            }
            _item.setDistrictId(_tmpDistrictId);
            final Integer _tmpUpazillaId;
            if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
              _tmpUpazillaId = null;
            } else {
              _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
            }
            _item.setUpazillaId(_tmpUpazillaId);
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            _item.setLatitude(_tmpLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            _item.setLongitude(_tmpLongitude);
            final String _tmpOperatingHours;
            if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
              _tmpOperatingHours = null;
            } else {
              _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
            }
            _item.setOperatingHours(_tmpOperatingHours);
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            _item.setRating(_tmpRating);
            final int _tmpTotalReviews;
            _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
            _item.setTotalReviews(_tmpTotalReviews);
            final double _tmpCommissionPercentage;
            _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
            _item.setCommissionPercentage(_tmpCommissionPercentage);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item.setActive(_tmpIsActive);
            final boolean _tmpIsVerified;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
            _tmpIsVerified = _tmp_1 != 0;
            _item.setVerified(_tmpIsVerified);
            final String _tmpProfileImageUrl;
            if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
              _tmpProfileImageUrl = null;
            } else {
              _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
            }
            _item.setProfileImageUrl(_tmpProfileImageUrl);
            final String _tmpCoverImageUrl;
            if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
              _tmpCoverImageUrl = null;
            } else {
              _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
            }
            _item.setCoverImageUrl(_tmpCoverImageUrl);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _item.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _item.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpDivisionName;
            if (_cursor.isNull(_cursorIndexOfDivisionName)) {
              _tmpDivisionName = null;
            } else {
              _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
            }
            _item.setDivisionName(_tmpDivisionName);
            final String _tmpDistrictName;
            if (_cursor.isNull(_cursorIndexOfDistrictName)) {
              _tmpDistrictName = null;
            } else {
              _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
            }
            _item.setDistrictName(_tmpDistrictName);
            final String _tmpUpazillaName;
            if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
              _tmpUpazillaName = null;
            } else {
              _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
            }
            _item.setUpazillaName(_tmpUpazillaName);
            final double _tmpDistance;
            _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
            _item.setDistance(_tmpDistance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<LaundryShopEntity>> getShopsInBounds(final double minLat,
      final double maxLat, final double minLng, final double maxLng) {
    final String _sql = "SELECT * FROM laundry_shops WHERE is_active = 1 AND latitude BETWEEN ? AND ? AND longitude BETWEEN ? AND ? ORDER BY rating DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minLat);
    _argIndex = 2;
    _statement.bindDouble(_argIndex, maxLat);
    _argIndex = 3;
    _statement.bindDouble(_argIndex, minLng);
    _argIndex = 4;
    _statement.bindDouble(_argIndex, maxLng);
    return __db.getInvalidationTracker().createLiveData(new String[] {"laundry_shops"}, false, new Callable<List<LaundryShopEntity>>() {
      @Override
      @Nullable
      public List<LaundryShopEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
          final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
          final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
          final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
          final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
          final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
          final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
          final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
          final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
          final List<LaundryShopEntity> _result = new ArrayList<LaundryShopEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final LaundryShopEntity _item;
            _item = new LaundryShopEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            _item.setName(_tmpName);
            final String _tmpBnName;
            if (_cursor.isNull(_cursorIndexOfBnName)) {
              _tmpBnName = null;
            } else {
              _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
            }
            _item.setBnName(_tmpBnName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _item.setBnDescription(_tmpBnDescription);
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            _item.setOwnerName(_tmpOwnerName);
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            _item.setPhone(_tmpPhone);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            _item.setEmail(_tmpEmail);
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item.setAddress(_tmpAddress);
            final Integer _tmpDivisionId;
            if (_cursor.isNull(_cursorIndexOfDivisionId)) {
              _tmpDivisionId = null;
            } else {
              _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
            }
            _item.setDivisionId(_tmpDivisionId);
            final Integer _tmpDistrictId;
            if (_cursor.isNull(_cursorIndexOfDistrictId)) {
              _tmpDistrictId = null;
            } else {
              _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
            }
            _item.setDistrictId(_tmpDistrictId);
            final Integer _tmpUpazillaId;
            if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
              _tmpUpazillaId = null;
            } else {
              _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
            }
            _item.setUpazillaId(_tmpUpazillaId);
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            _item.setLatitude(_tmpLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            _item.setLongitude(_tmpLongitude);
            final String _tmpOperatingHours;
            if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
              _tmpOperatingHours = null;
            } else {
              _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
            }
            _item.setOperatingHours(_tmpOperatingHours);
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            _item.setRating(_tmpRating);
            final int _tmpTotalReviews;
            _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
            _item.setTotalReviews(_tmpTotalReviews);
            final double _tmpCommissionPercentage;
            _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
            _item.setCommissionPercentage(_tmpCommissionPercentage);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item.setActive(_tmpIsActive);
            final boolean _tmpIsVerified;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
            _tmpIsVerified = _tmp_1 != 0;
            _item.setVerified(_tmpIsVerified);
            final String _tmpProfileImageUrl;
            if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
              _tmpProfileImageUrl = null;
            } else {
              _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
            }
            _item.setProfileImageUrl(_tmpProfileImageUrl);
            final String _tmpCoverImageUrl;
            if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
              _tmpCoverImageUrl = null;
            } else {
              _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
            }
            _item.setCoverImageUrl(_tmpCoverImageUrl);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _item.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _item.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpDivisionName;
            if (_cursor.isNull(_cursorIndexOfDivisionName)) {
              _tmpDivisionName = null;
            } else {
              _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
            }
            _item.setDivisionName(_tmpDivisionName);
            final String _tmpDistrictName;
            if (_cursor.isNull(_cursorIndexOfDistrictName)) {
              _tmpDistrictName = null;
            } else {
              _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
            }
            _item.setDistrictName(_tmpDistrictName);
            final String _tmpUpazillaName;
            if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
              _tmpUpazillaName = null;
            } else {
              _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
            }
            _item.setUpazillaName(_tmpUpazillaName);
            final double _tmpDistance;
            _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
            _item.setDistance(_tmpDistance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<LaundryShopEntity> getShopsInBoundsSync(final double minLat, final double maxLat,
      final double minLng, final double maxLng) {
    final String _sql = "SELECT * FROM laundry_shops WHERE is_active = 1 AND latitude BETWEEN ? AND ? AND longitude BETWEEN ? AND ? ORDER BY rating DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minLat);
    _argIndex = 2;
    _statement.bindDouble(_argIndex, maxLat);
    _argIndex = 3;
    _statement.bindDouble(_argIndex, minLng);
    _argIndex = 4;
    _statement.bindDouble(_argIndex, maxLng);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
      final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
      final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
      final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
      final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
      final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
      final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
      final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
      final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
      final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
      final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
      final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
      final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
      final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
      final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
      final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
      final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
      final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
      final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
      final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
      final List<LaundryShopEntity> _result = new ArrayList<LaundryShopEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final LaundryShopEntity _item;
        _item = new LaundryShopEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpName;
        if (_cursor.isNull(_cursorIndexOfName)) {
          _tmpName = null;
        } else {
          _tmpName = _cursor.getString(_cursorIndexOfName);
        }
        _item.setName(_tmpName);
        final String _tmpBnName;
        if (_cursor.isNull(_cursorIndexOfBnName)) {
          _tmpBnName = null;
        } else {
          _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
        }
        _item.setBnName(_tmpBnName);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpBnDescription;
        if (_cursor.isNull(_cursorIndexOfBnDescription)) {
          _tmpBnDescription = null;
        } else {
          _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
        }
        _item.setBnDescription(_tmpBnDescription);
        final String _tmpOwnerName;
        if (_cursor.isNull(_cursorIndexOfOwnerName)) {
          _tmpOwnerName = null;
        } else {
          _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
        }
        _item.setOwnerName(_tmpOwnerName);
        final String _tmpPhone;
        if (_cursor.isNull(_cursorIndexOfPhone)) {
          _tmpPhone = null;
        } else {
          _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
        }
        _item.setPhone(_tmpPhone);
        final String _tmpEmail;
        if (_cursor.isNull(_cursorIndexOfEmail)) {
          _tmpEmail = null;
        } else {
          _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
        }
        _item.setEmail(_tmpEmail);
        final String _tmpAddress;
        if (_cursor.isNull(_cursorIndexOfAddress)) {
          _tmpAddress = null;
        } else {
          _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
        }
        _item.setAddress(_tmpAddress);
        final Integer _tmpDivisionId;
        if (_cursor.isNull(_cursorIndexOfDivisionId)) {
          _tmpDivisionId = null;
        } else {
          _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
        }
        _item.setDivisionId(_tmpDivisionId);
        final Integer _tmpDistrictId;
        if (_cursor.isNull(_cursorIndexOfDistrictId)) {
          _tmpDistrictId = null;
        } else {
          _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
        }
        _item.setDistrictId(_tmpDistrictId);
        final Integer _tmpUpazillaId;
        if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
          _tmpUpazillaId = null;
        } else {
          _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
        }
        _item.setUpazillaId(_tmpUpazillaId);
        final double _tmpLatitude;
        _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
        _item.setLatitude(_tmpLatitude);
        final double _tmpLongitude;
        _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
        _item.setLongitude(_tmpLongitude);
        final String _tmpOperatingHours;
        if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
          _tmpOperatingHours = null;
        } else {
          _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
        }
        _item.setOperatingHours(_tmpOperatingHours);
        final double _tmpRating;
        _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
        _item.setRating(_tmpRating);
        final int _tmpTotalReviews;
        _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
        _item.setTotalReviews(_tmpTotalReviews);
        final double _tmpCommissionPercentage;
        _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
        _item.setCommissionPercentage(_tmpCommissionPercentage);
        final boolean _tmpIsActive;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp != 0;
        _item.setActive(_tmpIsActive);
        final boolean _tmpIsVerified;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
        _tmpIsVerified = _tmp_1 != 0;
        _item.setVerified(_tmpIsVerified);
        final String _tmpProfileImageUrl;
        if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
          _tmpProfileImageUrl = null;
        } else {
          _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
        }
        _item.setProfileImageUrl(_tmpProfileImageUrl);
        final String _tmpCoverImageUrl;
        if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
          _tmpCoverImageUrl = null;
        } else {
          _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
        }
        _item.setCoverImageUrl(_tmpCoverImageUrl);
        final Date _tmpCreatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
        _item.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
        _item.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpDivisionName;
        if (_cursor.isNull(_cursorIndexOfDivisionName)) {
          _tmpDivisionName = null;
        } else {
          _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
        }
        _item.setDivisionName(_tmpDivisionName);
        final String _tmpDistrictName;
        if (_cursor.isNull(_cursorIndexOfDistrictName)) {
          _tmpDistrictName = null;
        } else {
          _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
        }
        _item.setDistrictName(_tmpDistrictName);
        final String _tmpUpazillaName;
        if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
          _tmpUpazillaName = null;
        } else {
          _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
        }
        _item.setUpazillaName(_tmpUpazillaName);
        final double _tmpDistance;
        _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
        _item.setDistance(_tmpDistance);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<LaundryShopEntity>> getTopRatedShops(final int limit) {
    final String _sql = "SELECT * FROM laundry_shops WHERE is_active = 1 AND total_reviews >= 5 ORDER BY rating DESC, total_reviews DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return __db.getInvalidationTracker().createLiveData(new String[] {"laundry_shops"}, false, new Callable<List<LaundryShopEntity>>() {
      @Override
      @Nullable
      public List<LaundryShopEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
          final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
          final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
          final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
          final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
          final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
          final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
          final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
          final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
          final List<LaundryShopEntity> _result = new ArrayList<LaundryShopEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final LaundryShopEntity _item;
            _item = new LaundryShopEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            _item.setName(_tmpName);
            final String _tmpBnName;
            if (_cursor.isNull(_cursorIndexOfBnName)) {
              _tmpBnName = null;
            } else {
              _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
            }
            _item.setBnName(_tmpBnName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _item.setBnDescription(_tmpBnDescription);
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            _item.setOwnerName(_tmpOwnerName);
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            _item.setPhone(_tmpPhone);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            _item.setEmail(_tmpEmail);
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item.setAddress(_tmpAddress);
            final Integer _tmpDivisionId;
            if (_cursor.isNull(_cursorIndexOfDivisionId)) {
              _tmpDivisionId = null;
            } else {
              _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
            }
            _item.setDivisionId(_tmpDivisionId);
            final Integer _tmpDistrictId;
            if (_cursor.isNull(_cursorIndexOfDistrictId)) {
              _tmpDistrictId = null;
            } else {
              _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
            }
            _item.setDistrictId(_tmpDistrictId);
            final Integer _tmpUpazillaId;
            if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
              _tmpUpazillaId = null;
            } else {
              _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
            }
            _item.setUpazillaId(_tmpUpazillaId);
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            _item.setLatitude(_tmpLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            _item.setLongitude(_tmpLongitude);
            final String _tmpOperatingHours;
            if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
              _tmpOperatingHours = null;
            } else {
              _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
            }
            _item.setOperatingHours(_tmpOperatingHours);
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            _item.setRating(_tmpRating);
            final int _tmpTotalReviews;
            _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
            _item.setTotalReviews(_tmpTotalReviews);
            final double _tmpCommissionPercentage;
            _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
            _item.setCommissionPercentage(_tmpCommissionPercentage);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item.setActive(_tmpIsActive);
            final boolean _tmpIsVerified;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
            _tmpIsVerified = _tmp_1 != 0;
            _item.setVerified(_tmpIsVerified);
            final String _tmpProfileImageUrl;
            if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
              _tmpProfileImageUrl = null;
            } else {
              _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
            }
            _item.setProfileImageUrl(_tmpProfileImageUrl);
            final String _tmpCoverImageUrl;
            if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
              _tmpCoverImageUrl = null;
            } else {
              _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
            }
            _item.setCoverImageUrl(_tmpCoverImageUrl);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _item.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _item.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpDivisionName;
            if (_cursor.isNull(_cursorIndexOfDivisionName)) {
              _tmpDivisionName = null;
            } else {
              _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
            }
            _item.setDivisionName(_tmpDivisionName);
            final String _tmpDistrictName;
            if (_cursor.isNull(_cursorIndexOfDistrictName)) {
              _tmpDistrictName = null;
            } else {
              _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
            }
            _item.setDistrictName(_tmpDistrictName);
            final String _tmpUpazillaName;
            if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
              _tmpUpazillaName = null;
            } else {
              _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
            }
            _item.setUpazillaName(_tmpUpazillaName);
            final double _tmpDistance;
            _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
            _item.setDistance(_tmpDistance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<LaundryShopEntity> getAllShopsSync() {
    final String _sql = "SELECT * FROM laundry_shops WHERE is_active = 1 ORDER BY rating DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
      final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
      final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
      final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
      final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
      final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
      final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
      final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
      final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
      final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
      final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
      final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
      final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
      final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
      final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
      final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
      final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
      final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
      final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
      final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
      final List<LaundryShopEntity> _result = new ArrayList<LaundryShopEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final LaundryShopEntity _item;
        _item = new LaundryShopEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpName;
        if (_cursor.isNull(_cursorIndexOfName)) {
          _tmpName = null;
        } else {
          _tmpName = _cursor.getString(_cursorIndexOfName);
        }
        _item.setName(_tmpName);
        final String _tmpBnName;
        if (_cursor.isNull(_cursorIndexOfBnName)) {
          _tmpBnName = null;
        } else {
          _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
        }
        _item.setBnName(_tmpBnName);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpBnDescription;
        if (_cursor.isNull(_cursorIndexOfBnDescription)) {
          _tmpBnDescription = null;
        } else {
          _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
        }
        _item.setBnDescription(_tmpBnDescription);
        final String _tmpOwnerName;
        if (_cursor.isNull(_cursorIndexOfOwnerName)) {
          _tmpOwnerName = null;
        } else {
          _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
        }
        _item.setOwnerName(_tmpOwnerName);
        final String _tmpPhone;
        if (_cursor.isNull(_cursorIndexOfPhone)) {
          _tmpPhone = null;
        } else {
          _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
        }
        _item.setPhone(_tmpPhone);
        final String _tmpEmail;
        if (_cursor.isNull(_cursorIndexOfEmail)) {
          _tmpEmail = null;
        } else {
          _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
        }
        _item.setEmail(_tmpEmail);
        final String _tmpAddress;
        if (_cursor.isNull(_cursorIndexOfAddress)) {
          _tmpAddress = null;
        } else {
          _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
        }
        _item.setAddress(_tmpAddress);
        final Integer _tmpDivisionId;
        if (_cursor.isNull(_cursorIndexOfDivisionId)) {
          _tmpDivisionId = null;
        } else {
          _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
        }
        _item.setDivisionId(_tmpDivisionId);
        final Integer _tmpDistrictId;
        if (_cursor.isNull(_cursorIndexOfDistrictId)) {
          _tmpDistrictId = null;
        } else {
          _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
        }
        _item.setDistrictId(_tmpDistrictId);
        final Integer _tmpUpazillaId;
        if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
          _tmpUpazillaId = null;
        } else {
          _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
        }
        _item.setUpazillaId(_tmpUpazillaId);
        final double _tmpLatitude;
        _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
        _item.setLatitude(_tmpLatitude);
        final double _tmpLongitude;
        _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
        _item.setLongitude(_tmpLongitude);
        final String _tmpOperatingHours;
        if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
          _tmpOperatingHours = null;
        } else {
          _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
        }
        _item.setOperatingHours(_tmpOperatingHours);
        final double _tmpRating;
        _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
        _item.setRating(_tmpRating);
        final int _tmpTotalReviews;
        _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
        _item.setTotalReviews(_tmpTotalReviews);
        final double _tmpCommissionPercentage;
        _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
        _item.setCommissionPercentage(_tmpCommissionPercentage);
        final boolean _tmpIsActive;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp != 0;
        _item.setActive(_tmpIsActive);
        final boolean _tmpIsVerified;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
        _tmpIsVerified = _tmp_1 != 0;
        _item.setVerified(_tmpIsVerified);
        final String _tmpProfileImageUrl;
        if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
          _tmpProfileImageUrl = null;
        } else {
          _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
        }
        _item.setProfileImageUrl(_tmpProfileImageUrl);
        final String _tmpCoverImageUrl;
        if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
          _tmpCoverImageUrl = null;
        } else {
          _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
        }
        _item.setCoverImageUrl(_tmpCoverImageUrl);
        final Date _tmpCreatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
        _item.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
        _item.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpDivisionName;
        if (_cursor.isNull(_cursorIndexOfDivisionName)) {
          _tmpDivisionName = null;
        } else {
          _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
        }
        _item.setDivisionName(_tmpDivisionName);
        final String _tmpDistrictName;
        if (_cursor.isNull(_cursorIndexOfDistrictName)) {
          _tmpDistrictName = null;
        } else {
          _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
        }
        _item.setDistrictName(_tmpDistrictName);
        final String _tmpUpazillaName;
        if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
          _tmpUpazillaName = null;
        } else {
          _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
        }
        _item.setUpazillaName(_tmpUpazillaName);
        final double _tmpDistance;
        _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
        _item.setDistance(_tmpDistance);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<LaundryShopEntity>> getAllShopsDebug() {
    final String _sql = "SELECT * FROM laundry_shops ORDER BY id";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"laundry_shops"}, false, new Callable<List<LaundryShopEntity>>() {
      @Override
      @Nullable
      public List<LaundryShopEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "division_id");
          final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "district_id");
          final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazilla_id");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfOperatingHours = CursorUtil.getColumnIndexOrThrow(_cursor, "operating_hours");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTotalReviews = CursorUtil.getColumnIndexOrThrow(_cursor, "total_reviews");
          final int _cursorIndexOfCommissionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "commission_percentage");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "is_verified");
          final int _cursorIndexOfProfileImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profile_image_url");
          final int _cursorIndexOfCoverImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cover_image_url");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
          final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
          final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
          final int _cursorIndexOfDistance = CursorUtil.getColumnIndexOrThrow(_cursor, "distance");
          final List<LaundryShopEntity> _result = new ArrayList<LaundryShopEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final LaundryShopEntity _item;
            _item = new LaundryShopEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            _item.setName(_tmpName);
            final String _tmpBnName;
            if (_cursor.isNull(_cursorIndexOfBnName)) {
              _tmpBnName = null;
            } else {
              _tmpBnName = _cursor.getString(_cursorIndexOfBnName);
            }
            _item.setBnName(_tmpBnName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _item.setBnDescription(_tmpBnDescription);
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            _item.setOwnerName(_tmpOwnerName);
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            _item.setPhone(_tmpPhone);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            _item.setEmail(_tmpEmail);
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item.setAddress(_tmpAddress);
            final Integer _tmpDivisionId;
            if (_cursor.isNull(_cursorIndexOfDivisionId)) {
              _tmpDivisionId = null;
            } else {
              _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
            }
            _item.setDivisionId(_tmpDivisionId);
            final Integer _tmpDistrictId;
            if (_cursor.isNull(_cursorIndexOfDistrictId)) {
              _tmpDistrictId = null;
            } else {
              _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
            }
            _item.setDistrictId(_tmpDistrictId);
            final Integer _tmpUpazillaId;
            if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
              _tmpUpazillaId = null;
            } else {
              _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
            }
            _item.setUpazillaId(_tmpUpazillaId);
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            _item.setLatitude(_tmpLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            _item.setLongitude(_tmpLongitude);
            final String _tmpOperatingHours;
            if (_cursor.isNull(_cursorIndexOfOperatingHours)) {
              _tmpOperatingHours = null;
            } else {
              _tmpOperatingHours = _cursor.getString(_cursorIndexOfOperatingHours);
            }
            _item.setOperatingHours(_tmpOperatingHours);
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            _item.setRating(_tmpRating);
            final int _tmpTotalReviews;
            _tmpTotalReviews = _cursor.getInt(_cursorIndexOfTotalReviews);
            _item.setTotalReviews(_tmpTotalReviews);
            final double _tmpCommissionPercentage;
            _tmpCommissionPercentage = _cursor.getDouble(_cursorIndexOfCommissionPercentage);
            _item.setCommissionPercentage(_tmpCommissionPercentage);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item.setActive(_tmpIsActive);
            final boolean _tmpIsVerified;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsVerified);
            _tmpIsVerified = _tmp_1 != 0;
            _item.setVerified(_tmpIsVerified);
            final String _tmpProfileImageUrl;
            if (_cursor.isNull(_cursorIndexOfProfileImageUrl)) {
              _tmpProfileImageUrl = null;
            } else {
              _tmpProfileImageUrl = _cursor.getString(_cursorIndexOfProfileImageUrl);
            }
            _item.setProfileImageUrl(_tmpProfileImageUrl);
            final String _tmpCoverImageUrl;
            if (_cursor.isNull(_cursorIndexOfCoverImageUrl)) {
              _tmpCoverImageUrl = null;
            } else {
              _tmpCoverImageUrl = _cursor.getString(_cursorIndexOfCoverImageUrl);
            }
            _item.setCoverImageUrl(_tmpCoverImageUrl);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _item.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _item.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpDivisionName;
            if (_cursor.isNull(_cursorIndexOfDivisionName)) {
              _tmpDivisionName = null;
            } else {
              _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
            }
            _item.setDivisionName(_tmpDivisionName);
            final String _tmpDistrictName;
            if (_cursor.isNull(_cursorIndexOfDistrictName)) {
              _tmpDistrictName = null;
            } else {
              _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
            }
            _item.setDistrictName(_tmpDistrictName);
            final String _tmpUpazillaName;
            if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
              _tmpUpazillaName = null;
            } else {
              _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
            }
            _item.setUpazillaName(_tmpUpazillaName);
            final double _tmpDistance;
            _tmpDistance = _cursor.getDouble(_cursorIndexOfDistance);
            _item.setDistance(_tmpDistance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public int getAllShopCount() {
    final String _sql = "SELECT COUNT(*) FROM laundry_shops";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<Integer> getActiveShopCount() {
    final String _sql = "SELECT COUNT(*) FROM laundry_shops WHERE is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"laundry_shops"}, false, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
