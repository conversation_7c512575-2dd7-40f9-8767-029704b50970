package com.mdsadrulhasan.gogolaundry.database.entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import java.util.Date;

/**
 * Shop Item entity for Room database
 */
@Entity(
    tableName = "shop_items",
    foreignKeys = {
        @ForeignKey(
            entity = LaundryShopEntity.class,
            parentColumns = "id",
            childColumns = "shop_id",
            onDelete = ForeignKey.CASCADE
        ),
        @ForeignKey(
            entity = ItemEntity.class,
            parentColumns = "id",
            childColumns = "item_id",
            onDelete = ForeignKey.CASCADE
        )
    },
    indices = {
        @Index(value = {"shop_id", "item_id"}, unique = true),
        @Index(value = "shop_id"),
        @Index(value = "item_id")
    }
)
public class ShopItemEntity {

    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "shop_id")
    private int shopId;

    @ColumnInfo(name = "item_id")
    private int itemId;

    @ColumnInfo(name = "custom_price")
    private Double customPrice; // Nullable - if null, use default item price

    @ColumnInfo(name = "is_available")
    private boolean isAvailable;

    @ColumnInfo(name = "estimated_hours")
    private int estimatedHours;

    @ColumnInfo(name = "created_at")
    private Date createdAt;

    // Additional fields for display purposes
    private String itemName;
    private String itemBnName;
    private String itemImageUrl;
    private String description;
    private String bnDescription;
    private double defaultPrice;
    private String serviceName;

    // Getters and setters

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public int getItemId() {
        return itemId;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    public Double getCustomPrice() {
        return customPrice;
    }

    public void setCustomPrice(Double customPrice) {
        this.customPrice = customPrice;
    }

    public boolean isAvailable() {
        return isAvailable;
    }

    public void setAvailable(boolean available) {
        isAvailable = available;
    }

    public int getEstimatedHours() {
        return estimatedHours;
    }

    public void setEstimatedHours(int estimatedHours) {
        this.estimatedHours = estimatedHours;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemBnName() {
        return itemBnName;
    }

    public void setItemBnName(String itemBnName) {
        this.itemBnName = itemBnName;
    }

    public String getItemImageUrl() {
        return itemImageUrl;
    }

    public void setItemImageUrl(String itemImageUrl) {
        this.itemImageUrl = itemImageUrl;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBnDescription() {
        return bnDescription;
    }

    public void setBnDescription(String bnDescription) {
        this.bnDescription = bnDescription;
    }

    public double getDefaultPrice() {
        return defaultPrice;
    }

    public void setDefaultPrice(double defaultPrice) {
        this.defaultPrice = defaultPrice;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    /**
     * Get the effective price (custom price if set, otherwise default price)
     */
    public double getEffectivePrice() {
        return customPrice != null ? customPrice : defaultPrice;
    }

    /**
     * Get the price (alias for getEffectivePrice for compatibility)
     */
    public double getPrice() {
        return getEffectivePrice();
    }
}
